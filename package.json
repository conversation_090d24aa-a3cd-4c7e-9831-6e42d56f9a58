{"name": "analyedge", "version": "1.0.0", "description": "🚀 AnalyEdge: A free web visitor analytics service built on Cloudflare Workers + D1", "keywords": ["analytics", "cloudflare", "workers", "d1", "visitor-tracking", "web-analytics"], "author": "AnalyEdge Team", "license": "MIT", "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "initSqlLocal": "npx wrangler d1 execute web_analytics --local --file=./schema.sql", "initSql": "npx wrangler d1 execute web_analytics  --file=./schema.sql --remote"}, "devDependencies": {"@cloudflare/workers-types": "^4.20231121.0", "@types/node": "^20.10.2", "typescript": "^5.0.4", "wrangler": "^3.0.0"}, "dependencies": {"hono": "^3.10.3"}}