# 🚀 AnalyEdge：基于 Cloudflare + Hono + D1 的网页访客统计服务

一个完全免费、轻量级的网页访客统计服务，基于 Cloudflare Workers + D1 数据库实现。

## ✨ 特性

- ⚡ **极速响应**：基于 Cloudflare Workers 全球边缘网络
- 🔒 **隐私保护**：无 Cookie 追踪，仅记录必要统计信息
- 💰 **完全免费**：基于 Cloudflare 免费套餐
- 🎯 **轻量集成**：仅需一行代码即可集成
- 📊 **实时统计**：支持 PV（页面访问量）和 UV（独立访客）统计
- 🌐 **多站点支持**：自动识别和管理不同域名

## 部署步骤

### 安装依赖

```bash
npm install -g wrangler
npm install hono
```

### 登录

跳转cloudflare网页授权
```bash
npx wrangler login
```

### 创建D1数据库：[web_analytics]

> 数据库名称为`web_analytics`，与`package.json`内保持一致

```bash
npx wrangler d1 create web_analytics
```

成功后显示：
```
✅ Successfully created DB web_analytics

[[d1_databases]]
binding = "DB" # available in your Worker on env.DB
database_name = "web_analytics"
database_id = "<unique-ID-for-your-database>"
```

### 配置worker和D1数据库绑定

将上个步骤返回的`unique-ID-for-your-database`写进`wrangler.toml`中

```toml
name = "analyedge"
main = "src/index.ts"
compatibility_date = "2024-06-14"

[[d1_databases]]
binding = "DB" # available in your Worker on env.DB
database_name = "web_analytics"
database_id = "<unique-ID-for-your-database>"
```

### 初始化D1数据库的表结构

```bash
npm run initSql
```

### 发布

```bash
npm run deploy
```

成功后显示：
```
> analyedge@1.0.0 deploy
> wrangler deploy

Proxy environment variables detected. We'll use your proxy for fetch requests.
 ⛅️ wrangler 3.18.0
-------------------
Your worker has access to the following bindings:
- D1 Databases:
  - DB: web_analytics (<unique-ID-for-your-database>)
Total Upload: 50.28 KiB / gzip: 12.23 KiB
Uploaded analyedge (1.29 sec)
Published analyedge (4.03 sec)
  https://analyedge.xxxxx.workers.dev
Current Deployment ID: xxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

## 🚀 快速开始

部署完成后，访问你的 Worker 地址即可看到美观的管理界面，包含完整的使用说明和实时测试功能。

### 1. 引入脚本

在 HTML 的 `</body>` 标签前添加以下代码：

```html
<!-- 基础集成 -->
<script defer src="https://your-worker-domain.workers.dev/js/index.min.js"></script>

<!-- 自定义配置 -->
<script defer
  src="https://your-worker-domain.workers.dev/js/index.min.js"
  data-base-url="https://your-worker-domain.workers.dev"
  data-page-pv-id="page_pv"
  data-page-uv-id="page_uv">
</script>
```

### 2. 显示统计数据

在需要显示统计的位置添加以下元素：

```html
本页访问人次: <span id="page_pv"></span>
本页访问人数: <span id="page_uv"></span>
```

### 3. 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `data-base-url` | 当前域名 | API 服务地址 |
| `data-page-pv-id` | `page_pv` | PV 显示元素的 ID |
| `data-page-uv-id` | `page_uv` | UV 显示元素的 ID |

## 📊 API 接口

### 访问统计 API
```
POST /api/visit
Content-Type: application/json

{
  "hostname": "example.com",
  "url": "/page-path",
  "referrer": "https://google.com",
  "pv": true,
  "uv": true
}
```

### 健康检查 API
```
GET /api/health
```

## 🎨 管理界面

部署后访问根路径即可看到：
- 📋 完整的集成说明
- 🧪 实时 API 测试功能
- 📊 当前页面统计展示
- 🔗 快速复制集成代码

## 📁 项目结构

```
analyedge/
├── src/                    # 后端源码
│   ├── index.ts           # 主入口文件，定义API路由
│   └── lib/               # 工具库
│       ├── dbutil.ts      # 数据库操作工具
│       ├── util.ts        # URL处理工具
│       └── homepage.ts    # 管理界面生成
├── schema.sql             # 数据库表结构
├── wrangler.toml          # Cloudflare Workers配置
├── package.json           # 项目配置
├── DEPLOYMENT.md          # 详细部署指南
└── example.html           # 使用示例
```

## 🔧 本地开发

```bash
# 初始化本地数据库
npm run initSqlLocal

# 启动开发服务器
npm run start

# 访问 http://localhost:8787
```

## 🛠️ 故障排除

### 数据库错误
如果遇到 "no such table" 错误，请确保已运行数据库初始化：
```bash
npm run initSql
```

### 类型错误
如果遇到 "Type 'undefined' not supported" 错误，通常是因为传递了空值。检查请求数据格式。

### 部署失败
确保：
1. 已正确登录 Cloudflare
2. `wrangler.toml` 中的数据库 ID 正确
3. 数据库已初始化

## 🎨 自定义

你可以修改以下文件来自定义服务：

- `src/lib/homepage.ts` - 管理界面样式和内容
- `src/index.ts` - API 逻辑和路由
- `schema.sql` - 数据库结构

## 📄 许可证

MIT License
