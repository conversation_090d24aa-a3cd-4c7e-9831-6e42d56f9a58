export function getHomePage(baseUrl: string): string {
  return `<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 AnalyEdge：基于 Cloudflare + D1 的 Web 访客统计服务</title>
    <meta name="keywords" content="AnalyEdge,访客统计,Cloudflare,网页计数,Analytics,边缘分析">
    <meta name="description" content="🚀AnalyEdge: 完全免费的 Web 访客统计服务，基于 Cloudflare Workers + D1 数据库实现，仅需一行代码即可集成访客统计功能。">
    
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6; color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
        .header { text-align: center; color: white; margin-bottom: 3rem; }
        .header h1 { font-size: 2.5rem; margin-bottom: 1rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.2rem; opacity: 0.9; }
        .main-content { display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 3rem; }
        .card {
            background: white; border-radius: 15px; padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .card h2 { color: #5a67d8; margin-bottom: 1rem; font-size: 1.5rem; }
        .integration-card { grid-column: span 2; }
        .code-block {
            background: #1a202c; color: #e2e8f0; padding: 1.5rem; border-radius: 10px;
            margin: 1rem 0; overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.9rem;
            position: relative;
        }
        .highlight { color: #ffd700; font-weight: bold; }
        .stats-demo {
            display: flex; justify-content: space-around; margin: 1.5rem 0;
            padding: 1rem; background: #f7fafc; border-radius: 10px;
            border-left: 4px solid #5a67d8;
        }
        .stat-item { text-align: center; }
        .stat-value { font-size: 2rem; font-weight: bold; color: #5a67d8; display: block; }
        .stat-label { color: #666; font-size: 0.9rem; }
        .features { grid-column: span 2; display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; }
        .feature { background: #f8f9ff; padding: 1.5rem; border-radius: 10px; border-left: 4px solid #5a67d8; }
        .feature h3 { color: #5a67d8; margin-bottom: 0.5rem; }
        .feature-icon { font-size: 2rem; margin-bottom: 0.5rem; display: block; }
        .btn {
            display: inline-block; background: #5a67d8; color: white;
            padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 8px;
            transition: background 0.3s ease; font-weight: 500; margin: 0.5rem;
            border: none; cursor: pointer;
        }
        .btn:hover { background: #4c51bf; }
        .btn-secondary { background: #e2e8f0; color: #4a5568; }
        .btn-secondary:hover { background: #cbd5e0; }
        .footer { text-align: center; color: white; opacity: 0.8; margin-top: 3rem; }
        .copy-btn {
            background: #48bb78; color: white; border: none;
            padding: 0.3rem 0.8rem; border-radius: 5px; cursor: pointer;
            font-size: 0.8rem; position: absolute; top: 0.5rem; right: 1rem;
        }
        .copy-btn:hover { background: #38a169; }
        .loading { color: #999; }
        .error { color: #e53e3e; }
        .success { color: #38a169; }
        @media (max-width: 768px) {
            .main-content { grid-template-columns: 1fr; }
            .integration-card, .features { grid-column: span 1; }
            .header h1 { font-size: 2rem; }
            .container { padding: 1rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AnalyEdge</h1>
            <p>基于 Cloudflare + D1 的免费 Web 访客统计服务</p>
        </div>

        <div class="main-content">
            <div class="card integration-card">
                <h2>⚡ 一行代码集成</h2>
                <p>选择适合您需求的版本：</p>

                <div style="margin: 1rem 0; display: flex; gap: 1rem; align-items: center;">
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="radio" name="version" value="js" checked onchange="switchVersion()" style="margin-right: 0.5rem;">
                        <strong>标准版 (index.js)</strong>
                    </label>
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="radio" name="version" value="min" onchange="switchVersion()" style="margin-right: 0.5rem;">
                        <strong>压缩版 (index.min.js)</strong>
                    </label>
                </div>

                <div class="code-block">
                    <button class="copy-btn" onclick="copyCode()">复制</button>
                    <div id="integration-code">
&lt;!-- 在 &lt;/body&gt; 标签前添加 --&gt;
&lt;script defer src="<span class="highlight" id="script-url">${baseUrl}/js/index.js</span>"&gt;&lt;/script&gt;

&lt;!-- 显示统计数据 --&gt;
本页访问人次: &lt;span id="page_pv"&gt;&lt;/span&gt;
本页访问人数: &lt;span id="page_uv"&gt;&lt;/span&gt;
                    </div>
                </div>

                <div id="version-info" style="margin: 1rem 0; padding: 1rem; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #2196f3;">
                    <div id="standard-info">
                        <strong>📄 标准版特点：</strong>
                        <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                            <li>代码可读性好，便于学习和调试</li>
                            <li>包含完整的功能和错误处理</li>
                            <li>适合开发环境和一般使用</li>
                        </ul>
                    </div>
                    <div id="minified-info" style="display: none;">
                        <strong>📦 压缩版特点：</strong>
                        <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                            <li>文件体积小，加载速度快</li>
                            <li>适合生产环境和高性能要求</li>
                            <li>功能完全相同，仅压缩了代码</li>
                        </ul>
                    </div>
                </div>

                <div class="stats-demo">
                    <div class="stat-item">
                        <span class="stat-value" id="page_pv">--</span>
                        <span class="stat-label">页面访问量 (PV)</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value" id="page_uv">--</span>
                        <span class="stat-label">独立访客 (UV)</span>
                    </div>
                </div>

                <div style="margin-top: 1.5rem;">
                    <button class="btn" onclick="testAPI()">测试 API</button>
                    <a href="${baseUrl}/api/health" class="btn btn-secondary" target="_blank">健康检查</a>
                    <a href="https://github.com/your-repo/analytics" class="btn btn-secondary" target="_blank">查看源码</a>
                </div>
            </div>

            <div class="card features">
                <div class="feature">
                    <span class="feature-icon">⚡</span>
                    <h3>极速响应</h3>
                    <p>基于 Cloudflare Workers，全球边缘网络，毫秒级响应</p>
                </div>
                <div class="feature">
                    <span class="feature-icon">🔒</span>
                    <h3>隐私保护</h3>
                    <p>无 Cookie 追踪，仅记录必要的访问统计信息</p>
                </div>
                <div class="feature">
                    <span class="feature-icon">💰</span>
                    <h3>完全免费</h3>
                    <p>基于 Cloudflare 免费套餐，无使用限制</p>
                </div>
                <div class="feature">
                    <span class="feature-icon">🎯</span>
                    <h3>轻量集成</h3>
                    <p>仅需一行代码，自动统计 PV/UV 数据</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🚀 基于 Cloudflare Workers + Hono + D1 构建</p>
            <p>💡 完全开源，欢迎贡献和自建部署</p>
        </div>
    </div>

    <script>
        // 切换版本功能
        function switchVersion() {
            const selectedVersion = document.querySelector('input[name="version"]:checked').value;
            const scriptUrl = document.getElementById('script-url');
            const standardInfo = document.getElementById('standard-info');
            const minifiedInfo = document.getElementById('minified-info');

            if (selectedVersion === 'min') {
                scriptUrl.textContent = '${baseUrl}/js/index.min.js';
                standardInfo.style.display = 'none';
                minifiedInfo.style.display = 'block';
            } else {
                scriptUrl.textContent = '${baseUrl}/js/index.js';
                standardInfo.style.display = 'block';
                minifiedInfo.style.display = 'none';
            }
        }

        // 复制代码功能
        function copyCode() {
            const codeElement = document.getElementById('integration-code');
            const textToCopy = codeElement.innerText;

            navigator.clipboard.writeText(textToCopy).then(() => {
                const btn = document.querySelector('.copy-btn');
                const originalText = btn.textContent;
                btn.textContent = '已复制!';
                btn.style.background = '#38a169';

                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#48bb78';
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制代码');
            });
        }

        // 手动测试 API 功能
        async function testAPI() {
            const btn = event.target;
            const originalText = btn.textContent;
            btn.textContent = '测试中...';
            btn.disabled = true;

            try {
                const response = await fetch('${baseUrl}/api/visit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        hostname: window.location.hostname,
                        url: window.location.pathname,
                        referrer: document.referrer,
                        pv: true,
                        uv: true
                    })
                });

                const data = await response.json();

                if (data.ret === 'OK') {
                    // 手动更新演示数据（注意：这会被AnalyEdge脚本覆盖）
                    document.getElementById('page_pv').textContent = data.data.pv || '--';
                    document.getElementById('page_uv').textContent = data.data.uv || '--';
                    btn.textContent = '测试成功!';
                    btn.style.background = '#38a169';
                } else {
                    throw new Error(data.message || '测试失败');
                }
            } catch (error) {
                console.error('API 测试失败:', error);
                btn.textContent = '测试失败';
                btn.style.background = '#e53e3e';
            }

            setTimeout(() => {
                btn.textContent = originalText;
                btn.style.background = '#5a67d8';
                btn.disabled = false;
            }, 3000);
        }

    </script>

    <!-- AnalyEdge 统计脚本 - 使用标准集成方式 -->
    <script defer src="${baseUrl}/js/index.min.js"></script>
</body>
</html>`;
}
