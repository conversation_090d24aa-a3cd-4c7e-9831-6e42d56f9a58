import { Hono } from 'hono';
import { cors } from 'hono/cors';

import {checkUrl, getUrlData} from './lib/util'
import { insertAndReturnId , insert } from './lib/dbutil';
import { getHomePage } from './lib/homepage';

type Bindings = {
  DB: D1Database
}

const app = new Hono<{ Bindings: Bindings }>()

// 主页路由 - 返回 AnalyEdge 展示页面
app.get("/", (c) => {
  const currentUrl = new URL(c.req.url);
  const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;
  return c.html(getHomePage(baseUrl));
});

// 健康检查端点
app.get("/api/health", (c) => {
  return c.json({
    status: "healthy",
    service: "AnalyEdge",
    version: "1.0.0",
    timestamp: new Date().toISOString()
  });
});

// 标准版 JS 文件服务
app.get("/js/index.js", (c) => {
  const currentUrl = new URL(c.req.url);
  const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;

  // 生成标准版分析脚本
  const jsContent = `(function(){
  console.info('AnalyEdge v1.0.0 - ${baseUrl}');

  const script = document.currentScript;
  let dataBaseUrl = script.getAttribute('data-base-url');
  let dataPagePvId = script.getAttribute('data-page-pv-id');
  let dataPageUvId = script.getAttribute('data-page-uv-id');

  const AnalyEdge = {};
  AnalyEdge.version = '1.0.0';
  let BASE_API_PATH = '${baseUrl}';

  AnalyEdge.page_pv_id = "page_pv";
  AnalyEdge.page_uv_id = "page_uv";

  if(dataBaseUrl) {
    BASE_API_PATH = dataBaseUrl;
  }
  if(dataPagePvId) {
    AnalyEdge.page_pv_id = dataPagePvId;
  }
  if(dataPageUvId) {
    AnalyEdge.page_uv_id = dataPageUvId;
  }

  AnalyEdge.init = async function () {
    const thisPage = getLocation(window.location.href);
    const pagePvEle = document.getElementById(AnalyEdge.page_pv_id);
    const pageUvEle = document.getElementById(AnalyEdge.page_uv_id);

    const queryData = {
      url: thisPage.pathname,
      hostname: thisPage.hostname,
      referrer: document.referrer
    }

    if (pagePvEle) {
      queryData.pv = true;
    }
    if (pageUvEle) {
      queryData.uv = true;
    }

    await fetchJson(\`\${BASE_API_PATH}/api/visit\`, queryData)
      .then((res) => {
        if (res.ret != 'OK') {
          console.error('AnalyEdge error:', res.message);
          return;
        }
        const resData = res.data;
        if (pagePvEle) {
          pagePvEle.innerText = resData.pv;
        }
        if (pageUvEle) {
          pageUvEle.innerText = resData.uv;
        }
      })
      .catch((err) => {
        console.log("AnalyEdge fetch error:", err);
      });
  };

  function fetchJson(url, data) {
    return new Promise((resolve) => {
      fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })
      .then(res => res.json())
      .then(function(data) {
        resolve(data);
      })
      .catch(err => {
        console.error(err);
      });
    });
  }

  const getLocation = function(href) {
    const l = document.createElement("a");
    l.href = href;
    return l;
  };

  if (typeof window !== 'undefined') {
    AnalyEdge.init();
    window.AnalyEdge = AnalyEdge;
  }
})();`;

  c.header('Content-Type', 'application/javascript');
  c.header('Cache-Control', 'public, max-age=1800'); // 标准版缓存30分钟
  return c.text(jsContent);
});

// 压缩版 JS 文件服务
app.get("/js/index.min.js", (c) => {
  const currentUrl = new URL(c.req.url);
  const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;

  // 生成压缩版分析脚本
  const jsContent = `(function(){'use strict';console.info('AnalyEdge v1.0.0 - ${baseUrl}');const s=document.currentScript;let u=s.getAttribute('data-base-url'),p=s.getAttribute('data-page-pv-id'),v=s.getAttribute('data-page-uv-id');const A={version:'1.0.0'};let b='${baseUrl}';A.p='page_pv';A.v='page_uv';if(u)b=u;if(p)A.p=p;if(v)A.v=v;A.init=async function(){try{const l=parseURL(window.location.href),pe=document.getElementById(A.p),ve=document.getElementById(A.v),d={url:l.pathname,hostname:l.hostname,referrer:document.referrer};if(pe)d.pv=true;if(ve)d.uv=true;const r=await fetch(\`\${b}/api/visit\`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(d)}).then(res=>res.json());if(r.ret==='OK'){if(pe&&r.data.pv!==undefined)pe.textContent=r.data.pv;if(ve&&r.data.uv!==undefined)ve.textContent=r.data.uv;}else{console.error('AnalyEdge error:',r.message);}}catch(e){console.error('AnalyEdge init failed:',e);}};function parseURL(href){const a=document.createElement('a');a.href=href;return{hostname:a.hostname,pathname:a.pathname};}if(typeof window!=='undefined'){window.AnalyEdge=A;A.init();}})();`;

  c.header('Content-Type', 'application/javascript');
  c.header('Cache-Control', 'public, max-age=3600'); // 压缩版缓存1小时
  return c.text(jsContent);
});

app.use('/api/*', cors());

app.post('/api/visit', async (c) => {
  const retObj = {ret: "ERROR", data: null, message: "Error, Internal Server Error"};
  try{
    let visitorIP = c.req.header('CF-Connecting-IP') || '127.0.0.1' // 提供默认值
    const body = await c.req.json()
    const hostname = body.hostname || 'localhost'
    const url_path = body.url || '/'
    const referrer = body.referrer || ''
    const pv = body.pv
    const uv = body.uv
    let referrer_path = ''
    let referrer_domain = ''
    if (referrer&&checkUrl(referrer)){
      const referrerData = getUrlData(referrer);
      referrer_domain = referrerData.hostname;
      referrer_path = referrerData.pathname;
    }
    const website  = await c.env.DB.prepare('select id, domain from t_website where domain = ?').bind(hostname).first();
    let websiteId: number;
    if (website){
      await insert(c.env.DB, 
        'insert into t_web_visitor (website_id, url_path, referrer_domain, referrer_path, visitor_ip) values(?, ?, ?, ?, ?)',
        [website.id, url_path, referrer_domain, referrer_path, visitorIP]);
      websiteId = Number(website.id);
    } else{
      websiteId = await insertAndReturnId(c.env.DB, 'insert into t_website (name, domain) values(?,?)',[hostname.split(".").join("_"), hostname]);
      await insert(c.env.DB, 
        'insert into t_web_visitor (website_id, url_path, referrer_domain, referrer_path, visitor_ip) values(?, ?, ?, ?, ?)', 
        [websiteId, url_path, referrer_domain, referrer_path, visitorIP]);
    }
    const resData:{pv?: number, uv?: number} = {}
    if (pv){
      const total = await c.env.DB.prepare('SELECT COUNT(*) AS total from t_web_visitor where website_id = ? and url_path = ?').bind(websiteId, url_path).first('total');
      resData['pv'] = Number(total)
    }
    if (uv){
      const total = await c.env.DB.prepare('SELECT COUNT(*) AS total from (select DISTINCT visitor_ip from t_web_visitor where website_id = ? and url_path = ?) t').bind(websiteId, url_path).first('total');
      resData['uv'] = Number(total)
    }
    return c.json({ret: "OK", data: resData});
  } catch (e) {
    console.error(e);
    return c.json(retObj);
  }
})


app.onError((err, c) => {
	console.error(`${err}`);
	return c.text(err.toString());
});

app.notFound(c => c.text('Not found', 404));
export default app