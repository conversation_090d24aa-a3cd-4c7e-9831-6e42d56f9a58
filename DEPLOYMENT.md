# 🚀 AnalyEdge 部署指南

## 快速部署

### 1. 环境准备

确保你已经安装了 Node.js (推荐 v20+) 和 npm。

```bash
# 克隆项目
git clone <your-repo-url>
cd analytics_with_cloudflare

# 安装依赖
npm install
```

### 2. Cloudflare 配置

#### 登录 Cloudflare
```bash
npx wrangler login
```

#### 创建 D1 数据库
```bash
npx wrangler d1 create web_analytics
```

成功后会显示数据库配置信息，复制 `database_id`。

#### 更新配置文件

编辑 `wrangler.toml`，将 `<unique-ID-for-your-database>` 替换为实际的数据库 ID：

```toml
name = "analyedge"
main = "src/index.ts"
compatibility_date = "2024-06-14"

[[d1_databases]]
binding = "DB"
database_name = "web_analytics"
database_id = "your-actual-database-id"
```

### 3. 初始化数据库

```bash
# 初始化远程数据库
npm run initSql

# 或者初始化本地数据库（用于开发）
npm run initSqlLocal
```

### 4. 部署

```bash
npm run deploy
```

部署成功后，你会得到一个 Worker URL，类似：
`https://analyedge.your-subdomain.workers.dev`

## 本地开发

```bash
# 启动本地开发服务器
npm run dev
# 或
npm run start
```

访问 `http://localhost:8787` 查看管理界面。

## 使用方法

### 基础集成

在你的网站中添加以下代码：

```html
<script defer src="https://your-worker-url.workers.dev/js/index.min.js"></script>

<!-- 显示统计 -->
本页访问人次: <span id="page_pv"></span>
本页访问人数: <span id="page_uv"></span>
```

### 自定义配置

```html
<script defer 
  src="https://your-worker-url.workers.dev/js/index.min.js"
  data-base-url="https://your-worker-url.workers.dev"
  data-page-pv-id="custom_pv_id"
  data-page-uv-id="custom_uv_id">
</script>
```

## 管理界面功能

部署后访问 Worker URL 根路径，你将看到：

- 📋 **集成指南**：完整的代码示例和说明
- 🧪 **API 测试**：实时测试统计功能
- 📊 **实时数据**：当前页面的访问统计
- 🔗 **快速复制**：一键复制集成代码
- ✅ **健康检查**：服务状态监控

## API 端点

- `GET /` - 管理界面
- `GET /js/index.min.js` - 统计脚本
- `POST /api/visit` - 记录访问
- `GET /api/health` - 健康检查

## 故障排除

### 数据库错误
如果遇到 "no such table" 错误，请确保已运行数据库初始化：
```bash
npm run initSql
```

### 类型错误
如果遇到 "Type 'undefined' not supported" 错误，通常是因为传递了空值。检查请求数据格式。

### 部署失败
确保：
1. 已正确登录 Cloudflare
2. `wrangler.toml` 中的数据库 ID 正确
3. 数据库已初始化

## 自定义

你可以修改以下文件来自定义服务：

- `src/lib/homepage.ts` - 管理界面样式和内容
- `src/index.ts` - API 逻辑和路由
- `schema.sql` - 数据库结构

## 许可证

MIT License - 详见 LICENSE 文件
